package com.qifafa.taobaoServer;

import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import fi.iki.elonen.NanoHTTPD;

/**
 * HTTP服务器
 * 基于NanoHTTPD实现RESTful API接口
 */
public class HttpServer extends NanoHTTPD {
    
    private static final String TAG = "TaobaoServer";
    private final Object hookObject;
    private final Gson gson;
    
    public HttpServer(int port, Object hookObject) {
        super(port);
        this.hookObject = hookObject;
        this.gson = new Gson();

    }
    
    @Override
    public Response serve(IHTTPSession session) {
        String uri = session.getUri();
        Method method = session.getMethod();
        
        XposedBridge.log(TAG + ": 收到请求 " + method + " " + uri);
        
        try {
            // 处理CORS预检请求
            if (method == Method.OPTIONS) {
                return createCORSResponse();
            }
            
            // 路由处理
            if (uri.equals("/secret") && method == Method.POST) {
                return handleSecretRequest(session);
            } else {
                return createErrorResponse(404, "Not Found");
            }
            
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 处理请求失败: " + e.getMessage());
            return createErrorResponse(500, "Internal Server Error: " + e.getMessage());
        }
    }
    private String readRequestBody(IHTTPSession session) throws ResponseException, IOException {
        final HashMap<String, String> map = new HashMap<String, String>();
        session.parseBody(map);
        final String json = map.get("postData");
        return json;
    }
    /**
     * 处理API请求
     */
    private Response handleSecretRequest(IHTTPSession session) {
        try {
            // 解析请求体
           String jsonBody = readRequestBody(session);
           ParamContext paramContext = gson.fromJson(jsonBody, ParamContext.class);
            Map<String, Object> map = new HashMap<>(paramContext.getMap());
            Map<String, String> mapPage = new HashMap<>(paramContext.getMapPage());
            String appKey = paramContext.getAppKey();
            String authCode = "";
            boolean useWua = paramContext.getNeedWua();
            String requestId = "";
            Object b = XposedHelpers.callMethod(hookObject,  "r",   map, mapPage, appKey, authCode, useWua, requestId);
            var responseText =  gson.toJson(b);
          return  createResponse(Response.Status.OK,"application/json",responseText);

        } catch (Exception e) {
            XposedBridge.log(TAG + ": API请求处理失败: " + e.getMessage());
            return createErrorResponse(500, "API Error: " + e.getMessage());
        }
    }

    /**
     * 创建CORS响应
     */
    private Response createCORSResponse() {
        Response response = createResponse(Response.Status.OK, "application/json", "");
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
        return response;
    }
    
    /**
     * 创建错误响应
     */
    private Response createErrorResponse(int code, String message) {
        JsonObject error = new JsonObject();
        error.addProperty("success", false);
        error.addProperty("error", message);
        error.addProperty("code", code);
        
        Response.Status status = Response.Status.OK;
        switch (code) {
            case 400: status = Response.Status.BAD_REQUEST; break;
            case 404: status = Response.Status.NOT_FOUND; break;
            case 405: status = Response.Status.METHOD_NOT_ALLOWED; break;
            case 500: status = Response.Status.INTERNAL_ERROR; break;
        }
        
        Response response = createResponse(status, "application/json", gson.toJson(error));
        response.addHeader("Access-Control-Allow-Origin", "*");
        return response;
    }
    
    /**
     * 创建响应
     */
    private Response createResponse(Response.Status status, String mimeType, String content) {
        return newFixedLengthResponse(status, mimeType, content);
    }
}
