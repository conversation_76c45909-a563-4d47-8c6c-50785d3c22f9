package com.qifafa.taobaoServer;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import java.util.Map;

import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

/**
 * Xposed插件主入口类
 * 负责初始化HTTP服务器和Hook管理器
 */
public class XposedMain implements IXposedHookLoadPackage {

    private static final String TAG = "TaobaoServer";
    @SuppressLint("StaticFieldLeak")
    private static HttpServer httpServer;
    public static boolean inited = false;
    private static final Object serverLock = new Object(); // 并发锁
    @SuppressLint("StaticFieldLeak")
    private static Context appContext; // 保存应用上下文用于Toast
    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {
        // 只在目标应用中启动服务
        if (lpparam.packageName.equalsIgnoreCase("com.taobao.taobao") && lpparam.processName.equals("com.taobao.taobao")) {
            XposedBridge.log(TAG+"：检测到目标应用: " + lpparam.packageName);
            taoBaoApiHook(lpparam);
        }
    }
    private void taoBaoApiHook(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable{

        final String finalHookClassName = "tb.n9f";
        XposedHelpers.findAndHookMethod(Application.class, "attach", Context.class, new XC_MethodHook() {
            @Override
            protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                ClassLoader cl = ((Context) param.args[0]).getClassLoader();
                appContext = (Context) param.args[0]; // 保存应用上下文
                    try {
                        Class<?> hookClass = cl.loadClass(finalHookClassName);
                        XposedHelpers.findAndHookMethod(hookClass, "f", Map.class, String.class, boolean.class, new XC_MethodHook() {
                            @Override
                            protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                                super.beforeHookedMethod(param);
                            }

                            @Override
                            protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                                super.afterHookedMethod(param);
                                if (inited) {
                                    return;
                                }
                                inited = true;
                                // 启动HTTP服务器
                                startHttpServer(param.thisObject);
                                // 注册退出监听
                                handleShutdownHook();

                            }
                        });
//                        XposedHelpers.findAndHookMethod(hookClass, "a", HashMap.class, HashMap.class, String.class, String.class, boolean.class, String.class, new XC_MethodHook() {
//
//                            @Override
//                            protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
//                                super.beforeHookedMethod(param);
//                                HashMap<String, String> secretParams = new HashMap<>();
//                                secretParams.put("map", JSON.toJSONString(param.args[0]));
//                                secretParams.put("mapPage", JSON.toJSONString(param.args[1]));
//                                secr etParams.put("appKey", Objects.toString(param.args[2]));
//                                Log.i("shoutao", "签名参数: " + JSON.toJSONString(secretParams));
//                            }
//
//                            @Override
//                            protected void afterHookedMethod(MethodHookParam param) throws Throwable {
//                                super.afterHookedMethod(param);
//                                Log.i("shoutao", "签名结果: " + JSON.toJSONString(param.getResult()));
//                            }
//                        });
                    } catch (Exception e) {
                        Log.e(TAG, "loadClass 发生异常", e);
                        return;
                    }
//                Class<?> builderClass = cl.loadClass("mtopsdk.mtop.protocol.builder.impl.InnerProtocolParamBuilderImpl");
//                Class<?> mtopContextClass = cl.loadClass("mtopsdk.framework.domain.a");
//                XposedHelpers.findAndHookMethod(builderClass, "buildParams", mtopContextClass,
//                        new XC_MethodHook() {
//                            @Override
//                            protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
//                                super.beforeHookedMethod(param);
//                                Log.i("shoutao", " 组装参数的参数是：" + JSON.toJSONString(param.getResult()));
//                            }
//
//                            @Override
//                            protected void afterHookedMethod(MethodHookParam param) throws Throwable {
//                                super.afterHookedMethod(param);
//                            }
//                        });
//
//                Class<?> Converter = cl.loadClass("mtopsdk.mtop.protocol.converter.impl.AbstractNetworkConverter");
//                XposedHelpers.findAndHookMethod(Converter, "convert", mtopContextClass,
//                        new XC_MethodHook() {
//                            @Override
//                            protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
//                                super.beforeHookedMethod(param);
//                            }
//
//                            @Override
//                            protected void afterHookedMethod(MethodHookParam param) throws Throwable {
//                                super.afterHookedMethod(param);
//                                String result = JSON.toJSONString(param.getResult());
//                                JSONObject jsonObject = JSON.parseObject(result);
//                                Object url = jsonObject.get("a");
//                                Object headers = jsonObject.get("c");
//                                String urlStr = JSON.toJSONString(url);
//                                String headerUrl = JSON.toJSONString(headers);
//                                List<String> urlList = LogUtil.getStrList(urlStr, 4000);
//                                for (String us : urlList) {
//                                    Log.i("shoutao", "请求url: " + us);
//                                }
//                                List<String> headerList = LogUtil.getStrList(headerUrl, 4000);
//                                for (String hs : headerList) {
//                                    Log.i("shoutao", "请求header: " + hs);
//                                }
//                            }
//                        });


            }
        });
    }


    private void handleShutdownHook() {
        try {
            // 使用Runtime.addShutdownHook来监听进程退出
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                XposedBridge.log(TAG + "：应用进程即将退出");
                HttpServer server = getHttpServer();
                if (server != null) {
                    try {
                        server.closeAllConnections();
                        server.stop();
                        XposedBridge.log(TAG + ": HTTP服务器已关闭");
                    } catch (Exception e) {
                        XposedBridge.log(TAG + ": 关闭HTTP服务器失败: " + e.getMessage());
                    }
                }
            }));

            // 另外尝试Hook Application的onTerminate方法作为备用
            try {
                XposedHelpers.findAndHookMethod(Application.class, "onTerminate", new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        super.beforeHookedMethod(param);
                        XposedBridge.log(TAG + "：Application.onTerminate被调用");
                        HttpServer server = getHttpServer();
                        if (server != null) {
                            try {
                                server.closeAllConnections();
                                server.stop();
                                XposedBridge.log(TAG + ": HTTP服务器已关闭");
                            } catch (Exception e) {
                                XposedBridge.log(TAG + ": 关闭HTTP服务器失败: " + e.getMessage());
                            }
                        }
                    }
                });
            } catch (Exception e) {
                XposedBridge.log(TAG + ": Hook Application.onTerminate失败: " + e.getMessage());
            }

        } catch (Exception e) {
            XposedBridge.log(TAG + ": 注册退出监听失败: " + e.getMessage());
        }
    }
    /**
     * 启动HTTP服务器
     */
    private void startHttpServer(Object hookObject) {
        synchronized (serverLock) { // 使用并发锁确保线程安全
            try {
                if (httpServer == null) {
                    httpServer = new HttpServer(8080, hookObject);
                    httpServer.start();
                    XposedBridge.log(TAG + ": HTTP服务器启动成功，端口: 8080");

                    // 显示Toast提示服务已启用
                    showToast("签名服务已启用，端口: 8080");
                } else {
                    XposedBridge.log(TAG + ": HTTP服务器已经在运行中");
                }
            } catch (Exception e) {
                XposedBridge.log(TAG + ": HTTP服务器启动失败: " + e.getMessage());
                showToast("淘宝签名服务启动失败: " + e.getMessage());
            }
        }
    }

    /**
     * 获取HTTP服务器实例
     */
    public static HttpServer getHttpServer() {
        synchronized (serverLock) {
            return httpServer;
        }
    }

    /**
     * 显示Toast提示
     */
    private void showToast(String message) {
        if (appContext != null) {
            try {
                // 在主线程中显示Toast
                Handler mainHandler = new Handler(Looper.getMainLooper());
                mainHandler.post(() -> {
                    try {
                        Toast.makeText(appContext, message, Toast.LENGTH_LONG).show();
                    } catch (Exception e) {
                        XposedBridge.log(TAG + ": 显示Toast失败: " + e.getMessage());
                    }
                });
            } catch (Exception e) {
                XposedBridge.log(TAG + ": 创建Toast失败: " + e.getMessage());
            }
        }
    }


}
