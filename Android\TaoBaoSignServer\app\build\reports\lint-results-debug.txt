E:\Code\Taobao\Android\TaoBaoSignServer\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.2 is available: 8.12.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.12.0 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.2"
      ~~~~~~~
E:\Code\Taobao\Android\TaoBaoSignServer\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.2 is available: 8.12.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.12.0 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.2"
      ~~~~~~~
E:\Code\Taobao\Android\TaoBaoSignServer\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.2 is available: 8.12.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.12.0 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.2"
      ~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

E:\Code\Taobao\Android\TaoBaoSignServer\app\build.gradle:38: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'org.nanohttpd:nanohttpd:2.3.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\Code\Taobao\Android\TaoBaoSignServer\app\build.gradle:40: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.google.code.gson:gson:2.10.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

0 errors, 5 warnings
