plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.qifafa.taobaoServer'
    compileSdk 35

    defaultConfig {
        applicationId "com.qifafa.taobaoServer"
        minSdk 24
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    // Xposed API
    compileOnly files('src/lib/XposedBridgeAPI-89.jar')
    // HTTP Server - NanoHTTPD
    implementation 'org.nanohttpd:nanohttpd:2.3.1'
    // JSON处理
    implementation 'com.google.code.gson:gson:2.10.1'
}