<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.9.2">

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.9.2 is available: 8.12.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.12.0 is difficult: 8.9.3)"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.9.2&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="E:\Code\Taobao\Android\TaoBaoSignServer\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.9.2 is available: 8.12.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.12.0 is difficult: 8.9.3)"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.9.2&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="E:\Code\Taobao\Android\TaoBaoSignServer\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.9.2 is available: 8.12.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.12.0 is difficult: 8.9.3)"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.9.2&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="E:\Code\Taobao\Android\TaoBaoSignServer\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;org.nanohttpd:nanohttpd:2.3.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\Code\Taobao\Android\TaoBaoSignServer\app\build.gradle"
            line="38"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.google.code.gson:gson:2.10.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\Code\Taobao\Android\TaoBaoSignServer\app\build.gradle"
            line="40"
            column="20"/>
    </issue>

</issues>
