package com.qifafa.taobaoServer;

import java.util.Map;

public class ParamContext {
    private Map<String, Object> map;
    private Map<String, String> mapPage;
    private String appKey;
    private boolean needWua;

    public Map<String, Object> getMap() {
        return map;
    }

    public void setMap(Map<String, Object> map) {
        this.map = map;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public Map<String, String> getMapPage() {
        return mapPage;
    }

    public void setMapPage(Map<String, String> mapPage) {
        this.mapPage = mapPage;
    }

    public boolean getNeedWua() {
        return needWua;
    }

    public void setNeedWua(boolean needWua) {
        this.needWua = needWua;
    }
}